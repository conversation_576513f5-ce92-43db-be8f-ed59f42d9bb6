import os
import time

def monitor_progress():
    """Monitor the progress of the main script"""
    print("Monitoring script progress...")
    print("Press Ctrl+C to stop monitoring")
    
    try:
        while True:
            # Check if manual.csv exists and count lines
            if os.path.exists('manual.csv'):
                with open('manual.csv', 'r', encoding='utf-8') as f:
                    lines = f.readlines()
                    manual_count = len(lines) - 1  # Subtract header
                    print(f"Items saved to manual.csv: {manual_count}")
            else:
                print("No manual.csv file yet")
            
            time.sleep(10)  # Check every 10 seconds
            
    except KeyboardInterrupt:
        print("\nStopped monitoring")

if __name__ == "__main__":
    monitor_progress()
