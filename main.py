import pandas as pd
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
import re
import time
import os
import csv

# Thi<PERSON>t lập trình du<PERSON>ệt Brave
options = Options()
options.binary_location = '/usr/bin/brave-browser'
driver = webdriver.Chrome(options=options)

# Đ<PERSON>ng nhập hệ thống
driver.get('http://hrm.eteksofts.com/login')
driver.find_element(By.ID, 'emailaddress').send_keys('<EMAIL>')
driver.find_element(By.ID, 'password').send_keys('123456@')
driver.find_element(By.XPATH, "//button[text()='Đăng nhập']").click()
wait = WebDriverWait(driver, 10)

logout_button = wait.until(
    EC.presence_of_element_located((By.CLASS_NAME, 'btn-logout'))
)

# <PERSON><PERSON>ển tới ERP
driver.get('http://erp.eteksofts.com')
danh_muc_element = wait.until(
    EC.presence_of_element_located((By.XPATH, "//a[normalize-space(text())='Danh mục']"))
)

driver.get('http://erp.eteksofts.com/admin/products')

# Đọc dữ liệu từ CSV
df = pd.read_csv('book1.xlxs', encoding='utf-8', dtype={
    "Tên hàng": str,
    "Thuế suất VA": "Int64"
})

# Pattern trích model, tên sản phẩm
model_pattern = r'[Mm]odel[:：]?\s*([A-Za-z0-9.\-_/+\s]+)(?=\s*(?:[,.;]|hãng|nhà|NSX|xuất|dùng|hoạt|điện|mới|hàng|$))'

# Hàm ghi dòng vào manual.csv
def append_row_to_csv(row, filename="manual.csv"):
    file_exists = os.path.isfile(filename)
    fieldnames = list(row.index)
    with open(filename, "a", newline='', encoding="utf-8") as f:
        writer = csv.DictWriter(f, fieldnames=fieldnames)
        if not file_exists:
            writer.writeheader()
        writer.writerow(row.to_dict())

def get_product_name_before_model(text):
    if pd.isna(text):
        return ""
    parts = re.split(r'\b[,]?[Mm]odel[:\s]*', text, maxsplit=1)
    return parts[0].strip() if len(parts) > 1 else text.strip()

# Vòng lặp xử lý từng dòng
for index, row in df.iterrows():
    product_name = str(row['Tên hàng'])
    real_name = get_product_name_before_model(product_name)

    matches = re.findall(model_pattern, product_name, re.IGNORECASE)
    print(f"Model tìm thấy: {matches}")
    for match in matches:
        model = match.strip()
        model = ' '.join(model.split()).rstrip('.,;')

        if not model or len(model) <= 1:
            print(f"Không tìm thấy model hợp lệ trong sản phẩm: {real_name}")
            append_row_to_csv(row)
            continue

        vat = row['Thuế suất VA']
        if pd.isna(vat):
            print(f"Không tìm thấy VAT cho model: {model} trong sản phẩm: {product_name}")
            append_row_to_csv(row)
            input("Nhấn Enter để tiếp tục...")
            continue
        else:
            vat = int(vat)

        model_input = wait.until(
            EC.presence_of_element_located((By.ID, 'model_name'))
        )
        model_input.clear()
        model_input.send_keys(model)
        model_input.send_keys('\n')

        time.sleep(3)  # Chờ load kết quả

        try:
            table_element = wait.until(
                EC.presence_of_element_located((By.XPATH, "//table[@id='product-list']"))
            )
            rows = table_element.find_elements(By.XPATH, ".//tbody/tr")
        except:
            print(f"Không tìm thấy bảng kết quả cho model: {model}")
            append_row_to_csv(row)
            continue

        if len(rows) > 1 or len(rows) == 0:
            print(f"Model: {model} - Không xác định được duy nhất, số dòng: {len(rows)}")
            append_row_to_csv(row)
            continue

        row_element = rows[0]

        try:
            options_button = WebDriverWait(driver, 3).until(
                EC.presence_of_element_located((By.XPATH, ".//button[contains(@class, 'dropdown-toggle')]"))
            )
            options_button.click()
        except:
            print(f"Không tìm thấy nút tùy chọn cho model: {model}")
            append_row_to_csv(row)
            continue

        edit_button = wait.until(
            EC.element_to_be_clickable((By.XPATH, "//a[@title='Sửa hàng hóa']"))
        )
        edit_button.click()

        input_vat = wait.until(
            EC.presence_of_element_located((By.XPATH, "//input[@ng-model='product.vat_percent']"))
        )
        before_vat = input_vat.get_attribute('value')
        
        input_name = wait.until(
            EC.presence_of_element_located((By.XPATH, "//input[@ng-model='product.name']"))
        )
        before_name = input_name.get_attribute('value')
        
        print(f"Tên trong hệ thống: {before_name}")
        print(f"Tên trong CSV: {real_name}")

        if str(before_vat) != str(vat):
            print(f"Model: {model} - VAT trước: {before_vat}, cần cập nhật thành: {vat}")
            input_vat.clear()
            input_vat.send_keys(vat)
            input("Nhấn Enter để tiếp tục...")
        else:
            driver.get('http://erp.eteksofts.com/admin/products')
            print(f"Model: {model} - VAT đã đúng, không cần cập nhật.")
        time.sleep(2)
