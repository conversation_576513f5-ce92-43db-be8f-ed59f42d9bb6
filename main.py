import pandas as pd
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
import re
import time
import os
import csv

# Thiết lập trình duyệt Chrome (hoặc Brave nếu có)
options = Options()
# Thử các đường dẫn phổ biến cho Brave trên Windows
brave_paths = [
    r"C:\Program Files\BraveSoftware\Brave-Browser\Application\brave.exe",
    r"C:\Program Files (x86)\BraveSoftware\Brave-Browser\Application\brave.exe",
    r"C:\Users\<USER>\AppData\Local\BraveSoftware\Brave-Browser\Application\brave.exe".format(os.getenv('USERNAME'))
]

# Kiểm tra xem Brave có tồn tại không
brave_found = False
for path in brave_paths:
    if os.path.exists(path):
        options.binary_location = path
        brave_found = True
        print(f"Đã tìm thấy Brave tại: {path}")
        break

if not brave_found:
    print("Không tìm thấy Brave, sử dụng Chrome mặc định")

try:
    driver = webdriver.Chrome(options=options)
except Exception as e:
    print(f"Lỗi khởi tạo trình duyệt: {e}")
    print("Vui lòng cài đặt ChromeDriver hoặc kiểm tra đường dẫn Brave")
    exit(1)

# Đăng nhập hệ thống
try:
    print("Đang đăng nhập vào hệ thống...")
    driver.get('http://hrm.eteksofts.com/login')

    # Chờ trang load và nhập thông tin đăng nhập
    wait = WebDriverWait(driver, 10)

    email_field = wait.until(EC.presence_of_element_located((By.ID, 'emailaddress')))
    email_field.send_keys('<EMAIL>')

    password_field = driver.find_element(By.ID, 'password')
    password_field.send_keys('123456@')

    login_button = driver.find_element(By.XPATH, "//button[text()='Đăng nhập']")
    login_button.click()

    # Chờ đăng nhập thành công
    logout_button = wait.until(
        EC.presence_of_element_located((By.CLASS_NAME, 'btn-logout'))
    )
    print("Đăng nhập thành công!")

    # Chuyển tới ERP
    print("Đang chuyển tới ERP...")
    driver.get('http://erp.eteksofts.com')
    danh_muc_element = wait.until(
        EC.presence_of_element_located((By.XPATH, "//a[normalize-space(text())='Danh mục']"))
    )

    driver.get('http://erp.eteksofts.com/admin/products')
    print("Đã vào trang quản lý sản phẩm")

except Exception as e:
    print(f"Lỗi trong quá trình đăng nhập: {e}")
    driver.quit()
    exit(1)

# Đọc dữ liệu từ Excel
try:
    # Thử đọc file Excel
    if os.path.exists('Book1.xlsx'):
        # Đọc file Excel với header=None vì dòng đầu là dữ liệu, không phải header
        df = pd.read_excel('Book1.xlsx', header=None)
        # Đặt tên cột dựa trên cấu trúc file
        df.columns = ['Mã hàng', 'Tên hàng', 'Thuế suất VA', 'Nhà sản xuất']
        print(f"Đã đọc {len(df)} dòng từ Book1.xlsx")
        print(f"Các cột: {list(df.columns)}")

        # Chuyển đổi kiểu dữ liệu
        df['Tên hàng'] = df['Tên hàng'].astype(str)
        df['Thuế suất VA'] = pd.to_numeric(df['Thuế suất VA'], errors='coerce')

    elif os.path.exists('book1.xlsx'):
        df = pd.read_excel('book1.xlsx', header=None)
        df.columns = ['Mã hàng', 'Tên hàng', 'Thuế suất VA', 'Nhà sản xuất']
        print(f"Đã đọc {len(df)} dòng từ book1.xlsx")

        # Chuyển đổi kiểu dữ liệu
        df['Tên hàng'] = df['Tên hàng'].astype(str)
        df['Thuế suất VA'] = pd.to_numeric(df['Thuế suất VA'], errors='coerce')
    else:
        print("Không tìm thấy file Book1.xlsx hoặc book1.xlsx")
        print("Các file có sẵn:")
        for file in os.listdir('.'):
            if file.endswith(('.xlsx', '.csv')):
                print(f"  - {file}")
        exit(1)

    # Hiển thị một vài dòng đầu để kiểm tra
    print("\nMột vài dòng đầu tiên:")
    print(df.head(3))

except Exception as e:
    print(f"Lỗi đọc file Excel: {e}")
    print("Vui lòng kiểm tra định dạng file và cài đặt openpyxl: pip install openpyxl")
    exit(1)

# Pattern trích model, tên sản phẩm
model_pattern = r'[Mm]odel[:：]?\s*([A-Za-z0-9.\-_/+\s]+)(?=\s*(?:[,.;]|hãng|nhà|NSX|xuất|dùng|hoạt|điện|mới|hàng|$))'

# Hàm ghi dòng vào manual.csv
def append_row_to_csv(row, filename="manual.csv"):
    file_exists = os.path.isfile(filename)
    fieldnames = list(row.index)
    with open(filename, "a", newline='', encoding="utf-8") as f:
        writer = csv.DictWriter(f, fieldnames=fieldnames)
        if not file_exists:
            writer.writeheader()
        writer.writerow(row.to_dict())

def get_product_name_before_model(text):
    if pd.isna(text):
        return ""
    parts = re.split(r'\b[,]?[Mm]odel[:\s]*', text, maxsplit=1)
    return parts[0].strip() if len(parts) > 1 else text.strip()

# Vòng lặp xử lý từng dòng
print(f"\nBắt đầu xử lý {len(df)} sản phẩm...")
processed_count = 0
error_count = 0

for index, row in df.iterrows():
    try:
        processed_count += 1
        product_name = str(row['Tên hàng'])
        real_name = get_product_name_before_model(product_name)

        print(f"\n[{processed_count}/{len(df)}] Đang xử lý: {real_name}")

        matches = re.findall(model_pattern, product_name, re.IGNORECASE)
        print(f"Model tìm thấy: {matches}")

        if not matches:
            print(f"Không tìm thấy model trong sản phẩm: {real_name}")
            append_row_to_csv(row)
            continue

        for match in matches:
            model = match.strip()
            model = ' '.join(model.split()).rstrip('.,;')

            if not model or len(model) <= 1:
                print(f"Model không hợp lệ: '{model}' trong sản phẩm: {real_name}")
                append_row_to_csv(row)
                continue

            vat = row['Thuế suất VA']
            if pd.isna(vat):
                print(f"Không tìm thấy VAT cho model: {model} trong sản phẩm: {product_name}")
                append_row_to_csv(row)
                input("Nhấn Enter để tiếp tục...")
                continue
            else:
                vat = int(vat)

            print(f"Đang tìm kiếm model: {model} với VAT: {vat}%")

            # Tìm kiếm model trong hệ thống
            try:
                model_input = wait.until(
                    EC.presence_of_element_located((By.ID, 'model_name'))
                )
                model_input.clear()
                model_input.send_keys(model)
                model_input.send_keys('\n')

                time.sleep(3)  # Chờ load kết quả

                table_element = wait.until(
                    EC.presence_of_element_located((By.XPATH, "//table[@id='product-list']"))
                )
                rows = table_element.find_elements(By.XPATH, ".//tbody/tr")

                if len(rows) > 1 or len(rows) == 0:
                    print(f"Model: {model} - Không xác định được duy nhất, số dòng: {len(rows)}")
                    append_row_to_csv(row)
                    continue

                row_element = rows[0]
                print(f"Tìm thấy 1 sản phẩm khớp với model: {model}")

                # Mở menu tùy chọn
                options_button = WebDriverWait(driver, 3).until(
                    EC.element_to_be_clickable((By.XPATH, ".//button[contains(@class, 'dropdown-toggle')]"))
                )
                options_button.click()

                # Click nút sửa
                edit_button = wait.until(
                    EC.element_to_be_clickable((By.XPATH, "//a[@title='Sửa hàng hóa']"))
                )
                edit_button.click()

                # Lấy thông tin hiện tại
                input_vat = wait.until(
                    EC.presence_of_element_located((By.XPATH, "//input[@ng-model='product.vat_percent']"))
                )
                before_vat = input_vat.get_attribute('value')

                input_name = wait.until(
                    EC.presence_of_element_located((By.XPATH, "//input[@ng-model='product.name']"))
                )
                before_name = input_name.get_attribute('value')

                print(f"Tên trong hệ thống: {before_name}")
                print(f"Tên trong CSV: {real_name}")

                # So sánh và cập nhật VAT nếu cần
                if str(before_vat) != str(vat):
                    print(f"Model: {model} - VAT trước: {before_vat}%, cần cập nhật thành: {vat}%")
                    input_vat.clear()
                    input_vat.send_keys(str(vat))
                    print("Đã cập nhật VAT. Nhấn Enter để tiếp tục hoặc Ctrl+C để dừng...")
                    input()
                else:
                    print(f"Model: {model} - VAT đã đúng ({before_vat}%), không cần cập nhật.")

                # Quay về trang danh sách
                driver.get('http://erp.eteksofts.com/admin/products')
                time.sleep(2)

            except Exception as search_error:
                print(f"Lỗi khi tìm kiếm/cập nhật model {model}: {search_error}")
                append_row_to_csv(row)
                # Quay về trang danh sách nếu có lỗi
                try:
                    driver.get('http://erp.eteksofts.com/admin/products')
                    time.sleep(2)
                except:
                    pass
                continue

    except Exception as e:
        error_count += 1
        print(f"Lỗi xử lý dòng {processed_count}: {e}")
        append_row_to_csv(row)
        # Thử quay về trang chính nếu có lỗi
        try:
            driver.get('http://erp.eteksofts.com/admin/products')
            time.sleep(2)
        except:
            pass
        continue

print(f"\nHoàn thành! Đã xử lý {processed_count} sản phẩm, có {error_count} lỗi.")
print("Các sản phẩm cần xử lý thủ công đã được lưu vào file manual.csv")

# Đóng trình duyệt
try:
    driver.quit()
    print("Đã đóng trình duyệt.")
except:
    pass
