import pandas as pd
import os

# <PERSON><PERSON><PERSON> tra file Excel
try:
    if os.path.exists('Book1.xlsx'):
        df = pd.read_excel('Book1.xlsx')
        print("Cấu trúc file Book1.xlsx:")
        print(f"Số dòng: {len(df)}")
        print(f"Số cột: {len(df.columns)}")
        print("\nTên các cột:")
        for i, col in enumerate(df.columns):
            print(f"  {i}: '{col}'")
        
        print("\n5 dòng đầu tiên:")
        print(df.head())
        
        print("\nKiểu dữ liệu các cột:")
        print(df.dtypes)
        
    else:
        print("Không tìm thấy file Book1.xlsx")
        
except Exception as e:
    print(f"Lỗi: {e}")
